import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fetch from 'node-fetch'
import readline from 'readline'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const callOllama = async (question) => {
  try {
    console.log(`\n🔍 Processing question: "${question}"`)

    const { data, error } = await supabase.rpc('fn_get_related_table_data', {
      question_text: question,
    })

    if (error) {
      console.error('❌ Supabase RPC Error:', error)
      return
    }

    const fullData = data || {}
    const views = fullData?.views || {}
    const viewKeys = Object.keys(views)

    if (viewKeys.length === 0) {
      console.log('⚠️ No relevant data found for your question.')
      return
    }

    // Format data more cleanly - only show relevant fields
    let formattedViewData = ''
    let totalRecords = 0

    viewKeys.forEach((viewName) => {
      const rows = views[viewName]
      if (Array.isArray(rows) && rows.length > 0) {
        formattedViewData += `\n📋 Data from "${viewName}" (${rows.length} records):\n`
        rows.forEach((row, index) => {
          // Only show non-null values to reduce clutter
          const relevantData = Object.entries(row)
            .filter(([key, val]) => val !== null && val !== '')
            .map(([key, val]) => `${key}: ${val}`)
            .join(' | ')
          formattedViewData += `${index + 1}. ${relevantData}\n`
        })
        totalRecords += rows.length
      }
    })

    console.log(`\n📊 Found ${totalRecords} relevant records from ${viewKeys.length} view(s)`)

    const prompt = `You are a helpful assistant that analyzes database results and provides clear, structured answers.

Database Results:
${formattedViewData.trim()}

User Question: "${question}"

Instructions:
- Provide a direct, well-formatted answer based only on the data shown
- If the data doesn't fully match the question, explain what information is available
- Use bullet points or numbered lists for clarity
- Be concise but complete

Answer:`

    const ollamaRes = await fetch('http://localhost:11434/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama2:7b',
        messages: [{ role: 'user', content: prompt }],
        stream: false,
      }),
    })

    const ollamaData = await ollamaRes.json()

    const response = ollamaData?.message?.content
                  || ollamaData?.messages?.[0]?.content
                  || ollamaData?.response
                  || '⚠️ No usable content found in Ollama response.'

    console.log('\n🤖 Answer:\n', response)
    console.log('\n' + '='.repeat(60))

  } catch (err) {
    console.error('🔥 Unexpected Error:', err)
  }
}

// Interactive prompt function
const askQuestion = () => {
  rl.question('\n💬 Enter your question (or "exit" to quit): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }

    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestion()
      return
    }

    await callOllama(question.trim())
    askQuestion() // Ask for next question
  })
}

// Start the interactive session
console.log('🚀 Supabase-Ollama Query System Started!')
console.log('Ask questions about your database and get AI-powered answers.')
askQuestion()