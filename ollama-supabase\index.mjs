import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fetch from 'node-fetch'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

const callOllama = async (question) => {
  try {
    const { data, error } = await supabase.rpc('fn_get_related_table_data', {
      question_text: question,
    })

    if (error) {
      console.error('❌ Supabase RPC Error:', error)
      return
    }

    const fullData = data || {}
    const views = fullData?.views || {}
    const viewKeys = Object.keys(views)

    let formattedViewData = ''

    viewKeys.forEach((viewName) => {
      const rows = views[viewName]
      if (Array.isArray(rows) && rows.length > 0) {
        formattedViewData += `\n📋 Data from view: "${viewName}":\n`
        rows.forEach((row, index) => {
          const rowSummary = Object.entries(row)
            .map(([key, val]) => `${key}: ${val}`)
            .join(' | ')
          formattedViewData += `${index + 1}. ${rowSummary}\n`
        })
      }
    })

    const prompt = `
You are an assistant helping extract structured answers from database output.

Below is the data:
${formattedViewData.trim()}

Question: "${question}"

Please provide a clear, direct answer using only the relevant values.
    `.trim()

    console.log('\n📨 Final Prompt to Ollama:\n', prompt)

    const ollamaRes = await fetch('http://localhost:11434/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama2:7b',  // change this to 'llama2:7b' if needed
        messages: [{ role: 'user', content: prompt }],
        stream: false,
      }),
    })

    const ollamaData = await ollamaRes.json()

    console.log('\n🛠 Raw Ollama Response:\n', JSON.stringify(ollamaData, null, 2))

    const response = ollamaData?.message?.content 
                  || ollamaData?.messages?.[0]?.content 
                  || ollamaData?.response 
                  || '⚠️ No usable content found in Ollama response.'

    console.log('\n🤖 Ollama Response:\n', response)

  } catch (err) {
    console.error('🔥 Unexpected Error:', err)
  }
}

callOllama('List all person  names under different organization')