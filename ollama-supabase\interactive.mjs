import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fetch from 'node-fetch'
import readline from 'readline'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const callOllama = async (question) => {
  try {
    console.log(`\n🔍 Processing question: "${question}"`)

    // Try comprehensive data access first
    let { data, error } = await supabase.rpc('get_all_table_data', {
      question_text: question,
    })

    if (error) {
      console.log('⚠️ Comprehensive function not available, trying original...')

      // Fallback to original function
      const fallback = await supabase.rpc('fn_get_related_table_data', {
        question_text: question,
      })

      data = fallback.data
      error = fallback.error

      if (error) {
        console.error('❌ Supabase RPC Error:', error)
        return
      }
    }

    const fullData = data || {}
    const tables = fullData?.tables || {}
    const views = fullData?.views || {}
    const functions = fullData?.functions || {}
    const allDataSources = { ...tables, ...views }
    const dataKeys = Object.keys(allDataSources)

    if (dataKeys.length === 0 && Object.keys(functions).length === 0) {
      console.log('⚠️ No relevant data found for your question.')
      console.log('💡 Try typing "schema" to see available data sources.')
      console.log('🔍 Search terms used:', fullData?.search_terms || 'none')
      return
    }

    // Format data more cleanly - only show relevant fields
    let formattedData = ''
    let totalRecords = 0

    // Show matched tables and views info
    if (fullData?.matched_tables?.length > 0) {
      console.log(`\n🎯 Matched tables: ${fullData.matched_tables.join(', ')}`)
    }
    if (fullData?.matched_views?.length > 0) {
      console.log(`🎯 Matched views: ${fullData.matched_views.join(', ')}`)
    }

    dataKeys.forEach((sourceName) => {
      const rows = allDataSources[sourceName]
      if (Array.isArray(rows) && rows.length > 0) {
        const sourceType = tables[sourceName] ? 'table' : 'view'
        formattedData += `\n📋 Data from ${sourceType}: "${sourceName}" (${rows.length} records):\n`

        // Show first few records
        const recordsToShow = Math.min(rows.length, 5)
        rows.slice(0, recordsToShow).forEach((row, index) => {
          // Only show non-null values to reduce clutter
          const relevantData = Object.entries(row)
            .filter(([key, val]) => val !== null && val !== '')
            .map(([key, val]) => `${key}: ${val}`)
            .join(' | ')
          formattedData += `${index + 1}. ${relevantData}\n`
        })

        if (rows.length > recordsToShow) {
          formattedData += `... and ${rows.length - recordsToShow} more records\n`
        }

        totalRecords += rows.length
      }
    })

    // Add function information if available
    const functionKeys = Object.keys(functions)
    if (functionKeys.length > 0) {
      formattedData += `\n🔧 Available functions:\n`
      functionKeys.forEach(funcName => {
        const func = functions[funcName]
        formattedData += `- ${funcName} (${func.type})\n`
      })
    }

    console.log(`\n📊 Found ${totalRecords} relevant records from ${dataKeys.length} data source(s) and ${functionKeys.length} function(s)`)

    const prompt = `You are a helpful assistant that analyzes database results and provides clear, structured answers.

Database Results:
${formattedData.trim()}

User Question: "${question}"

Instructions:
- Provide a direct, well-formatted answer based only on the data shown
- If the data doesn't fully match the question, explain what information is available
- Use bullet points or numbered lists for clarity
- Be concise but complete
- If asked for counts, provide exact numbers from the data shown

Answer:`

    const ollamaRes = await fetch('http://localhost:11434/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama2:7b',
        messages: [{ role: 'user', content: prompt }],
        stream: false,
      }),
    })

    const ollamaData = await ollamaRes.json()

    const response = ollamaData?.message?.content
                  || ollamaData?.messages?.[0]?.content
                  || ollamaData?.response
                  || '⚠️ No usable content found in Ollama response.'

    console.log('\n🤖 Answer:\n', response)
    console.log('\n' + '='.repeat(60))

  } catch (err) {
    console.error('🔥 Unexpected Error:', err)
  }
}

// Interactive prompt function
const askQuestion = () => {
  rl.question('\n💬 Enter your question (or "exit" to quit): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }
    
    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestion()
      return
    }

    await callOllama(question.trim())
    askQuestion() // Ask for next question
  })
}

// Function to get comprehensive database schema
const getFullDatabaseSchema = async () => {
  try {
    console.log('\n🔍 Fetching complete database schema...')

    // Try to get all tables and views using SQL
    const { data: schemaInfo, error } = await supabase.rpc('get_full_schema_info')

    if (error) {
      console.log('⚠️ Custom schema function not found. Trying alternative methods...')

      // Alternative: Try to query information_schema directly
      try {
        const { data: tables } = await supabase
          .from('information_schema.tables')
          .select('table_name, table_type')
          .eq('table_schema', 'public')
          .order('table_name')

        const { data: views } = await supabase
          .from('information_schema.views')
          .select('table_name')
          .eq('table_schema', 'public')
          .order('table_name')

        console.log('\n📋 Database Schema:')
        if (tables) {
          console.log('\n📊 Tables:')
          tables.filter(t => t.table_type === 'BASE TABLE').forEach(table => {
            console.log(`  - ${table.table_name}`)
          })
        }

        if (views) {
          console.log('\n👁️ Views:')
          views.forEach(view => {
            console.log(`  - ${view.table_name}`)
          })
        }

        return { tables, views }

      } catch (infoError) {
        console.log('⚠️ Could not access information_schema. Checking current RPC function...')

        // Fallback to current RPC
        const { data: rpcData, error: rpcError } = await supabase.rpc('fn_get_related_table_data', {
          question_text: 'show all available data'
        })

        if (!rpcError && rpcData) {
          console.log('\n📋 Currently available data sources:')
          const views = rpcData?.views || {}
          Object.keys(views).forEach(viewName => {
            console.log(`  - ${viewName} (${views[viewName]?.length || 0} records)`)
          })
          return rpcData
        }
      }
    } else {
      console.log('\n📋 Complete Database Schema:')
      console.log(schemaInfo)
      return schemaInfo
    }
  } catch (err) {
    console.error('🔥 Error fetching schema:', err)
  }
}

// Function to create a comprehensive RPC function
const createComprehensiveRPC = async () => {
  console.log('\n🛠️ Creating comprehensive database access function...')

  const createFunctionSQL = `
CREATE OR REPLACE FUNCTION get_all_table_data(question_text TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
    table_record RECORD;
    view_record RECORD;
    table_data JSON;
    all_data JSON := '{}';
BEGIN
    -- Get data from all tables
    FOR table_record IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (SELECT * FROM %I LIMIT 100) t', table_record.table_name)
        INTO table_data;

        IF table_data IS NOT NULL THEN
            all_data := jsonb_set(all_data::jsonb, ('{tables,' || table_record.table_name || '}')::text[], table_data::jsonb);
        END IF;
    END LOOP;

    -- Get data from all views
    FOR view_record IN
        SELECT table_name
        FROM information_schema.views
        WHERE table_schema = 'public'
    LOOP
        EXECUTE format('SELECT json_agg(row_to_json(v)) FROM (SELECT * FROM %I LIMIT 100) v', view_record.table_name)
        INTO table_data;

        IF table_data IS NOT NULL THEN
            all_data := jsonb_set(all_data::jsonb, ('{views,' || view_record.table_name || '}')::text[], table_data::jsonb);
        END IF;
    END LOOP;

    RETURN all_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`

  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSQL })

    if (error) {
      console.log('⚠️ Could not create comprehensive function automatically.')
      console.log('📝 You need to create this function manually in your Supabase SQL editor:')
      console.log(createFunctionSQL)
    } else {
      console.log('✅ Comprehensive database access function created!')
    }
  } catch (err) {
    console.log('⚠️ Could not create function automatically.')
    console.log('📝 Please create this function manually in your Supabase SQL editor:')
    console.log(createFunctionSQL)
  }
}

// Start the interactive session
console.log('🚀 Supabase-Ollama Query System Started!')
console.log('Ask questions about your database and get AI-powered answers.')
console.log('\n📋 Available commands:')
console.log('  - Type "schema" to see all available tables/views')
console.log('  - Type "setup" to create comprehensive database access')
console.log('  - Type "count tablename" to get record count (e.g., "count sl_acti_quiz")')
console.log('  - Type "exit" to quit')
console.log('  - Ask any question about your data (tables, views, counts, etc.)')

// Enhanced askQuestion function
const askQuestionEnhanced = () => {
  rl.question('\n💬 Enter your question (or "schema"/"exit"): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }

    if (question.toLowerCase() === 'schema') {
      await getFullDatabaseSchema()
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase() === 'setup') {
      await createComprehensiveRPC()
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase().startsWith('count ')) {
      const tableName = question.substring(6).trim()
      console.log(`\n🔍 Getting count for table: ${tableName}`)

      try {
        // Try public schema first
        let { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })

        if (error) {
          // Try smart_learn schema
          console.log(`⚠️ Not found in public schema, trying smart_learn...`)

          const smartResult = await supabase
            .schema('smart_learn')
            .from(tableName)
            .select('*', { count: 'exact', head: true })

          if (smartResult.error) {
            console.error(`❌ Error accessing table "${tableName}" in both schemas:`)
            console.error(`Public: ${error.message}`)
            console.error(`Smart_learn: ${smartResult.error.message}`)
          } else {
            console.log(`✅ Table "smart_learn.${tableName}" has ${smartResult.count || 0} records`)

            // Get sample data from smart_learn
            const { data: smartSampleData } = await supabase
              .schema('smart_learn')
              .from(tableName)
              .select('*')
              .limit(3)

            if (smartSampleData && smartSampleData.length > 0) {
              console.log(`📋 Sample data from smart_learn.${tableName}:`)
              smartSampleData.forEach((row, index) => {
                const rowData = Object.entries(row)
                  .filter(([key, val]) => val !== null && val !== '')
                  .map(([key, val]) => `${key}: ${val}`)
                  .join(' | ')
                console.log(`  ${index + 1}. ${rowData}`)
              })
            }
          }
        } else {
          console.log(`✅ Table "public.${tableName}" has ${count || 0} records`)

          // Get sample data from public
          const { data: sampleData } = await supabase
            .from(tableName)
            .select('*')
            .limit(3)

          if (sampleData && sampleData.length > 0) {
            console.log(`📋 Sample data from public.${tableName}:`)
            sampleData.forEach((row, index) => {
              const rowData = Object.entries(row)
                .filter(([key, val]) => val !== null && val !== '')
                .map(([key, val]) => `${key}: ${val}`)
                .join(' | ')
              console.log(`  ${index + 1}. ${rowData}`)
            })
          }
        }
      } catch (err) {
        console.error(`🔥 Error: ${err.message}`)
      }

      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase().includes('quiz')) {
      // Try direct quiz queries
      console.log('\n🔍 Searching for quiz data...')

      try {
        // Try common quiz table names including your specific table
        const quizTables = ['quiz', 'quizzes', 'questions', 'quiz_questions', 'sl_acti_quiz', 'sl_quiz', 'activity_quiz']
        let foundAny = false

        for (const tableName of quizTables) {
          try {
            // Try public schema first
            let { data, error, count } = await supabase
              .from(tableName)
              .select('*', { count: 'exact', head: true })

            if (!error) {
              console.log(`✅ Found table "public.${tableName}" with ${count || 0} total records`)
              foundAny = true

              // Get sample data
              const { data: sampleData } = await supabase
                .from(tableName)
                .select('*')
                .limit(3)

              if (sampleData && sampleData.length > 0) {
                console.log(`📋 Sample data from public.${tableName}:`)
                sampleData.forEach((row, index) => {
                  const rowData = Object.entries(row)
                    .filter(([key, val]) => val !== null && val !== '')
                    .map(([key, val]) => `${key}: ${val}`)
                    .join(' | ')
                  console.log(`  ${index + 1}. ${rowData}`)
                })
              }
              console.log('')
            } else {
              // Try smart_learn schema
              try {
                const { data: smartData, error: smartError, count: smartCount } = await supabase
                  .schema('smart_learn')
                  .from(tableName)
                  .select('*', { count: 'exact', head: true })

                if (!smartError) {
                  console.log(`✅ Found table "smart_learn.${tableName}" with ${smartCount || 0} total records`)
                  foundAny = true

                  // Get sample data from smart_learn schema
                  const { data: smartSampleData } = await supabase
                    .schema('smart_learn')
                    .from(tableName)
                    .select('*')
                    .limit(3)

                  if (smartSampleData && smartSampleData.length > 0) {
                    console.log(`📋 Sample data from smart_learn.${tableName}:`)
                    smartSampleData.forEach((row, index) => {
                      const rowData = Object.entries(row)
                        .filter(([key, val]) => val !== null && val !== '')
                        .map(([key, val]) => `${key}: ${val}`)
                        .join(' | ')
                      console.log(`  ${index + 1}. ${rowData}`)
                    })
                  }
                  console.log('')
                }
              } catch (smartError) {
                console.log(`⚠️ Table "${tableName}" not found in smart_learn schema either`)
              }
            }
          } catch (tableError) {
            console.log(`⚠️ Table "${tableName}" not accessible in any schema`)
          }
        }

        if (!foundAny) {
          console.log('❌ No quiz tables found. Using RPC function...')
          await callOllama(question.trim())
        }

      } catch (err) {
        console.log('⚠️ Direct table access failed, using RPC...')
        console.error('Error details:', err.message)
        await callOllama(question.trim())
      }

      askQuestionEnhanced()
      return
    }

    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestionEnhanced()
      return
    }

    await callOllama(question.trim())
    askQuestionEnhanced() // Ask for next question
  })
}

askQuestionEnhanced()
