import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fetch from 'node-fetch'
import readline from 'readline'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

// Get all available schemas dynamically
const getAllSchemas = async () => {
  try {
    // Try to get all schemas using RPC
    const { data, error } = await supabase.rpc('sql', {
      query: 'SELECT schema_name FROM information_schema.schemata ORDER BY schema_name;'
    })

    if (!error && data) {
      return data.map(row => row.schema_name)
    }
  } catch (err) {
    // Fallback to common schemas if RPC fails
  }

  // Fallback list including your custom schema
  return ['public', 'smart_learn', 'auth', 'storage', 'extensions', 'graphql_public', 'information_schema']
}

// Auto-detect schema and find table
const findTableInSchemas = async (tableName) => {
  console.log(`🔍 Auto-detecting schema for table "${tableName}"...`)

  // Get all available schemas
  const schemas = await getAllSchemas()
  console.log(`📋 Checking schemas: ${schemas.join(', ')}`)

  for (const schema of schemas) {
    try {
      // Test if table exists by trying a simple query
      const { error } = await supabase
        .schema(schema)
        .from(tableName)
        .select('*')
        .limit(1)

      if (!error) {
        console.log(`✅ Found table "${tableName}" in schema "${schema}"`)
        return schema
      }
    } catch (err) {
      // Continue to next schema
      console.log(`⚠️ Schema "${schema}" not accessible or table not found`)
    }
  }

  console.log(`❌ Table "${tableName}" not found in any accessible schema`)
  return null
}

// Fast direct query function with auto schema detection
const executeSmartQuery = async (question) => {
  const lowerQuestion = question.toLowerCase()

  // Extract table name and search criteria from question
  const tableMatch = question.match(/from\s+(\w+)/i) || question.match(/in\s+(\w+)\s+table/i)
  const tableName = tableMatch ? tableMatch[1] : null

  // Extract search criteria
  const nameMatch = question.match(/name\s+is\s+['""]?([^'""\n]+)['""]?/i) || question.match(/where\s+name\s*=\s*['""]?([^'""\n]+)['""]?/i)
  const searchValue = nameMatch ? nameMatch[1].trim() : null

  if (tableName && searchValue) {
    console.log(`\n🎯 Direct query: Looking for "${searchValue}" in table "${tableName}"`)

    // Auto-detect which schema contains the table
    const foundSchema = await findTableInSchemas(tableName)

    if (foundSchema) {
      try {
        console.log(`🔍 Searching in ${foundSchema}.${tableName}...`)
        const { data, error } = await supabase
          .schema(foundSchema)
          .from(tableName)
          .select('id, name')
          .ilike('name', `%${searchValue}%`)
          .limit(10)

        if (!error && data && data.length > 0) {
          console.log(`\n✅ Found ${data.length} matching record(s) in ${foundSchema}.${tableName}:`)
          data.forEach((row, index) => {
            console.log(`${index + 1}. ID: ${row.id}, Name: ${row.name}`)
          })

          if (lowerQuestion.includes('return id') || lowerQuestion.includes('get id')) {
            console.log(`\n🎯 ANSWER: The ID for "${searchValue}" is ${data[0].id}`)
          }
          return true
        } else if (!error && data && data.length === 0) {
          console.log(`\n❌ No records found with name "${searchValue}" in ${foundSchema}.${tableName}`)

          // Show some sample data to help user
          const { data: sampleData } = await supabase
            .schema(foundSchema)
            .from(tableName)
            .select('id, name')
            .limit(5)

          if (sampleData && sampleData.length > 0) {
            console.log(`\n📋 Sample records in ${foundSchema}.${tableName}:`)
            sampleData.forEach((row, index) => {
              console.log(`${index + 1}. ID: ${row.id}, Name: ${row.name}`)
            })
          }
          return true
        } else {
          console.log(`❌ Error querying ${foundSchema}.${tableName}: ${error.message}`)
        }
      } catch (err) {
        console.log(`⚠️ Query failed: ${err.message}`)
      }
    }
  }

  // Handle count queries with auto schema detection
  if (lowerQuestion.includes('how many') || lowerQuestion.includes('count')) {
    const countTableMatch = question.match(/in\s+(\w+)/i) || question.match(/from\s+(\w+)/i)
    if (countTableMatch) {
      const countTable = countTableMatch[1]
      console.log(`\n🔢 Count query for ${countTable}...`)

      const foundSchema = await findTableInSchemas(countTable)
      if (foundSchema) {
        try {
          const { count, error } = await supabase
            .schema(foundSchema)
            .from(countTable)
            .select('*', { count: 'exact', head: true })

          if (!error) {
            console.log(`\n🎯 ANSWER: Table ${foundSchema}.${countTable} has ${count} records`)
            return true
          }
        } catch (err) {
          console.log(`❌ Count query failed for ${foundSchema}.${countTable}`)
        }
      }
    }
  }

  return false
}

const callOllama = async (question) => {
  try {
    console.log(`\n🔍 Processing question: "${question}"`)

    // Try smart direct query first
    const directResult = await executeSmartQuery(question)
    if (directResult) {
      return
    }

    console.log('⚠️ Direct query failed, trying RPC fallback (this may be slow)...')

    // Skip RPC for now and give helpful message
    console.log('\n💡 Suggestions:')
    console.log('1. Check if the table name is correct')
    console.log('2. Verify the record exists in the database')
    console.log('3. Try: "tables" to see available tables')
    console.log('4. Try: "count sl_category" to check if table has data')

    return

  } catch (err) {
    console.error('🔥 Unexpected Error:', err)
  }
}

// Interactive prompt function
const askQuestion = () => {
  rl.question('\n💬 Enter your question (or "exit" to quit): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }
    
    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestion()
      return
    }

    await callOllama(question.trim())
    askQuestion() // Ask for next question
  })
}

// Function to get comprehensive database schema
const getFullDatabaseSchema = async () => {
  try {
    console.log('\n🔍 Fetching complete database schema...')

    // Try to get all tables and views using SQL
    const { data: schemaInfo, error } = await supabase.rpc('get_full_schema_info')

    if (error) {
      console.log('⚠️ Custom schema function not found. Trying alternative methods...')

      // Alternative: Try to query information_schema directly
      try {
        const { data: tables } = await supabase
          .from('information_schema.tables')
          .select('table_name, table_type')
          .eq('table_schema', 'public')
          .order('table_name')

        const { data: views } = await supabase
          .from('information_schema.views')
          .select('table_name')
          .eq('table_schema', 'public')
          .order('table_name')

        console.log('\n📋 Database Schema:')
        if (tables) {
          console.log('\n📊 Tables:')
          tables.filter(t => t.table_type === 'BASE TABLE').forEach(table => {
            console.log(`  - ${table.table_name}`)
          })
        }

        if (views) {
          console.log('\n👁️ Views:')
          views.forEach(view => {
            console.log(`  - ${view.table_name}`)
          })
        }

        return { tables, views }

      } catch (infoError) {
        console.log('⚠️ Could not access information_schema. Checking current RPC function...')

        // Fallback to current RPC
        const { data: rpcData, error: rpcError } = await supabase.rpc('fn_get_related_table_data', {
          question_text: 'show all available data'
        })

        if (!rpcError && rpcData) {
          console.log('\n📋 Currently available data sources:')
          const views = rpcData?.views || {}
          Object.keys(views).forEach(viewName => {
            console.log(`  - ${viewName} (${views[viewName]?.length || 0} records)`)
          })
          return rpcData
        }
      }
    } else {
      console.log('\n📋 Complete Database Schema:')
      console.log(schemaInfo)
      return schemaInfo
    }
  } catch (err) {
    console.error('🔥 Error fetching schema:', err)
  }
}

// Function to create a comprehensive RPC function
const createComprehensiveRPC = async () => {
  console.log('\n🛠️ Creating comprehensive database access function...')

  const createFunctionSQL = `
CREATE OR REPLACE FUNCTION get_all_table_data(question_text TEXT)
RETURNS JSON AS $$
DECLARE
    result JSON;
    table_record RECORD;
    view_record RECORD;
    table_data JSON;
    all_data JSON := '{}';
BEGIN
    -- Get data from all tables
    FOR table_record IN
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_type = 'BASE TABLE'
    LOOP
        EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (SELECT * FROM %I LIMIT 100) t', table_record.table_name)
        INTO table_data;

        IF table_data IS NOT NULL THEN
            all_data := jsonb_set(all_data::jsonb, ('{tables,' || table_record.table_name || '}')::text[], table_data::jsonb);
        END IF;
    END LOOP;

    -- Get data from all views
    FOR view_record IN
        SELECT table_name
        FROM information_schema.views
        WHERE table_schema = 'public'
    LOOP
        EXECUTE format('SELECT json_agg(row_to_json(v)) FROM (SELECT * FROM %I LIMIT 100) v', view_record.table_name)
        INTO table_data;

        IF table_data IS NOT NULL THEN
            all_data := jsonb_set(all_data::jsonb, ('{views,' || view_record.table_name || '}')::text[], table_data::jsonb);
        END IF;
    END LOOP;

    RETURN all_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`

  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSQL })

    if (error) {
      console.log('⚠️ Could not create comprehensive function automatically.')
      console.log('📝 You need to create this function manually in your Supabase SQL editor:')
      console.log(createFunctionSQL)
    } else {
      console.log('✅ Comprehensive database access function created!')
    }
  } catch (err) {
    console.log('⚠️ Could not create function automatically.')
    console.log('📝 Please create this function manually in your Supabase SQL editor:')
    console.log(createFunctionSQL)
  }
}

// Start the interactive session
console.log('🚀 Supabase-Ollama Query System Started!')
console.log('Ask questions about your database and get AI-powered answers.')
console.log('\n📋 Available commands:')
console.log('  - Type "schemas" to see all available schemas')
console.log('  - Type "schema" to see all available tables/views')
console.log('  - Type "tables" to list all tables in smart_learn schema')
console.log('  - Type "setup" to create comprehensive database access')
console.log('  - Type "count tablename" to get record count (e.g., "count sl_acti_quiz")')
console.log('  - Type "exit" to quit')
console.log('  - Ask any question about your data (tables, views, counts, etc.)')

// Enhanced askQuestion function
const askQuestionEnhanced = () => {
  rl.question('\n💬 Enter your question (or "schema"/"exit"): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }

    if (question.toLowerCase() === 'schemas') {
      console.log('\n🔍 Discovering all available schemas...')
      const schemas = await getAllSchemas()
      console.log('\n📋 Available schemas:')
      schemas.forEach(schema => {
        console.log(`  - ${schema}`)
      })
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase() === 'schema') {
      await getFullDatabaseSchema()
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase() === 'setup') {
      await createComprehensiveRPC()
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase() === 'tables') {
      console.log('\n🔍 Listing all tables in smart_learn schema...')

      try {
        // Try to get tables using RPC
        const { data, error } = await supabase.rpc('sql', {
          query: `
            SELECT table_name, table_type
            FROM information_schema.tables
            WHERE table_schema = 'smart_learn'
            ORDER BY table_name;
          `
        })

        if (error) {
          console.log('⚠️ RPC method failed, trying alternative...')

          // Alternative: Try to query a known table to test access
          const testTables = ['sl_acti_quiz', 'users', 'activities', 'courses', 'lessons']

          console.log('\n📋 Testing common table names in smart_learn schema:')
          for (const tableName of testTables) {
            try {
              const { error: testError } = await supabase
                .schema('smart_learn')
                .from(tableName)
                .select('*', { count: 'exact', head: true })
                .limit(1)

              if (!testError) {
                console.log(`✅ ${tableName} - accessible`)
              } else {
                console.log(`❌ ${tableName} - ${testError.message}`)
              }
            } catch (err) {
              console.log(`❌ ${tableName} - ${err.message}`)
            }
          }
        } else {
          console.log('\n📋 Tables in smart_learn schema:')
          data.forEach(table => {
            console.log(`  - ${table.table_name} (${table.table_type})`)
          })
        }
      } catch (err) {
        console.error('🔥 Error listing tables:', err.message)
      }

      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase().startsWith('count ')) {
      const tableName = question.substring(6).trim()
      console.log(`\n🔍 Getting count for table: ${tableName}`)

      try {
        // Try public schema first
        let { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true })

        if (error) {
          // Try smart_learn schema
          console.log(`⚠️ Not found in public schema, trying smart_learn...`)

          const smartResult = await supabase
            .schema('smart_learn')
            .from(tableName)
            .select('*', { count: 'exact', head: true })

          if (smartResult.error) {
            console.error(`❌ Error accessing table "${tableName}" in both schemas:`)
            console.error(`Public: ${error.message}`)
            console.error(`Smart_learn: ${smartResult.error.message}`)
          } else {
            console.log(`✅ Table "smart_learn.${tableName}" has ${smartResult.count || 0} records`)

            // Get sample data from smart_learn
            const { data: smartSampleData } = await supabase
              .schema('smart_learn')
              .from(tableName)
              .select('*')
              .limit(3)

            if (smartSampleData && smartSampleData.length > 0) {
              console.log(`📋 Sample data from smart_learn.${tableName}:`)
              smartSampleData.forEach((row, index) => {
                const rowData = Object.entries(row)
                  .filter(([key, val]) => val !== null && val !== '')
                  .map(([key, val]) => `${key}: ${val}`)
                  .join(' | ')
                console.log(`  ${index + 1}. ${rowData}`)
              })
            }
          }
        } else {
          console.log(`✅ Table "public.${tableName}" has ${count || 0} records`)

          // Get sample data from public
          const { data: sampleData } = await supabase
            .from(tableName)
            .select('*')
            .limit(3)

          if (sampleData && sampleData.length > 0) {
            console.log(`📋 Sample data from public.${tableName}:`)
            sampleData.forEach((row, index) => {
              const rowData = Object.entries(row)
                .filter(([key, val]) => val !== null && val !== '')
                .map(([key, val]) => `${key}: ${val}`)
                .join(' | ')
              console.log(`  ${index + 1}. ${rowData}`)
            })
          }
        }
      } catch (err) {
        console.error(`🔥 Error: ${err.message}`)
      }

      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase().includes('quiz')) {
      // Try direct quiz queries
      console.log('\n🔍 Searching for quiz data in both schemas...')

      try {
        // Try common quiz table names including your specific table
        const quizTables = ['quiz', 'quizzes', 'questions', 'quiz_questions', 'sl_acti_quiz', 'sl_quiz', 'activity_quiz']
        let foundAny = false

        for (const tableName of quizTables) {
          let publicFound = false
          let smartFound = false

          // Try public schema first
          try {
            const { data, error, count } = await supabase
              .from(tableName)
              .select('*', { count: 'exact', head: true })

            if (!error && count !== null) {
              console.log(`✅ Found table "public.${tableName}" with ${count || 0} total records`)
              publicFound = true
              foundAny = true

              if (count > 0) {
                // Get sample data
                const { data: sampleData } = await supabase
                  .from(tableName)
                  .select('*')
                  .limit(3)

                if (sampleData && sampleData.length > 0) {
                  console.log(`📋 Sample data from public.${tableName}:`)
                  sampleData.forEach((row, index) => {
                    const rowData = Object.entries(row)
                      .filter(([key, val]) => val !== null && val !== '')
                      .map(([key, val]) => `${key}: ${val}`)
                      .join(' | ')
                    console.log(`  ${index + 1}. ${rowData}`)
                  })
                }
              }
            }
          } catch (publicError) {
            // Table doesn't exist in public schema
          }

          // Try smart_learn schema
          try {
            const { data: smartData, error: smartError, count: smartCount } = await supabase
              .schema('smart_learn')
              .from(tableName)
              .select('*', { count: 'exact', head: true })

            if (!smartError && smartCount !== null) {
              console.log(`✅ Found table "smart_learn.${tableName}" with ${smartCount || 0} total records`)
              smartFound = true
              foundAny = true

              if (smartCount > 0) {
                // Get sample data from smart_learn schema
                const { data: smartSampleData } = await supabase
                  .schema('smart_learn')
                  .from(tableName)
                  .select('*')
                  .limit(3)

                if (smartSampleData && smartSampleData.length > 0) {
                  console.log(`📋 Sample data from smart_learn.${tableName}:`)
                  smartSampleData.forEach((row, index) => {
                    const rowData = Object.entries(row)
                      .filter(([key, val]) => val !== null && val !== '')
                      .map(([key, val]) => `${key}: ${val}`)
                      .join(' | ')
                    console.log(`  ${index + 1}. ${rowData}`)
                  })
                }
              }
            }
          } catch (smartError) {
            // Table doesn't exist in smart_learn schema
          }

          // If table not found in either schema
          if (!publicFound && !smartFound) {
            console.log(`⚠️ Table "${tableName}" not found in any schema`)
          }

          if (publicFound || smartFound) {
            console.log('') // Add spacing between tables
          }
        }

        if (!foundAny) {
          console.log('❌ No quiz tables found in any schema. Using RPC function...')
          await callOllama(question.trim())
        }

      } catch (err) {
        console.log('⚠️ Direct table access failed, using RPC...')
        console.error('Error details:', err.message)
        await callOllama(question.trim())
      }

      askQuestionEnhanced()
      return
    }

    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestionEnhanced()
      return
    }

    await callOllama(question.trim())
    askQuestionEnhanced() // Ask for next question
  })
}

askQuestionEnhanced()
