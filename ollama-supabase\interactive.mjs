import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
import fetch from 'node-fetch'
import readline from 'readline'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const callOllama = async (question) => {
  try {
    console.log(`\n🔍 Processing question: "${question}"`)
    
    const { data, error } = await supabase.rpc('fn_get_related_table_data', {
      question_text: question,
    })

    if (error) {
      console.error('❌ Supabase RPC Error:', error)
      return
    }

    const fullData = data || {}
    const views = fullData?.views || {}
    const viewKeys = Object.keys(views)

    if (viewKeys.length === 0) {
      console.log('⚠️ No relevant data found for your question.')
      return
    }

    // Format data more cleanly - only show relevant fields
    let formattedViewData = ''
    let totalRecords = 0

    viewKeys.forEach((viewName) => {
      const rows = views[viewName]
      if (Array.isArray(rows) && rows.length > 0) {
        formattedViewData += `\n📋 Data from "${viewName}" (${rows.length} records):\n`
        rows.forEach((row, index) => {
          // Only show non-null values to reduce clutter
          const relevantData = Object.entries(row)
            .filter(([key, val]) => val !== null && val !== '')
            .map(([key, val]) => `${key}: ${val}`)
            .join(' | ')
          formattedViewData += `${index + 1}. ${relevantData}\n`
        })
        totalRecords += rows.length
      }
    })

    console.log(`\n📊 Found ${totalRecords} relevant records from ${viewKeys.length} view(s)`)

    const prompt = `You are a helpful assistant that analyzes database results and provides clear, structured answers.

Database Results:
${formattedViewData.trim()}

User Question: "${question}"

Instructions:
- Provide a direct, well-formatted answer based only on the data shown
- If the data doesn't fully match the question, explain what information is available
- Use bullet points or numbered lists for clarity
- Be concise but complete

Answer:`

    const ollamaRes = await fetch('http://localhost:11434/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama2:7b',
        messages: [{ role: 'user', content: prompt }],
        stream: false,
      }),
    })

    const ollamaData = await ollamaRes.json()

    const response = ollamaData?.message?.content 
                  || ollamaData?.messages?.[0]?.content 
                  || ollamaData?.response 
                  || '⚠️ No usable content found in Ollama response.'

    console.log('\n🤖 Answer:\n', response)
    console.log('\n' + '='.repeat(60))

  } catch (err) {
    console.error('🔥 Unexpected Error:', err)
  }
}

// Interactive prompt function
const askQuestion = () => {
  rl.question('\n💬 Enter your question (or "exit" to quit): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }
    
    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestion()
      return
    }

    await callOllama(question.trim())
    askQuestion() // Ask for next question
  })
}

// Function to list all tables and views
const listDatabaseSchema = async () => {
  try {
    console.log('\n🔍 Fetching database schema...')

    // Get all tables
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')

    if (tablesError) {
      console.log('⚠️ Could not fetch tables directly. Trying RPC...')

      // Try using RPC to get schema info
      const { data: schemaData, error: rpcError } = await supabase.rpc('fn_get_related_table_data', {
        question_text: 'show all tables and views in database schema'
      })

      if (rpcError) {
        console.error('❌ Error fetching schema:', rpcError)
        return
      }

      console.log('\n📋 Available data sources:')
      const views = schemaData?.views || {}
      Object.keys(views).forEach(viewName => {
        console.log(`  - ${viewName} (${views[viewName]?.length || 0} records)`)
      })
    } else {
      console.log('\n📋 Database Tables and Views:')
      tables.forEach(table => {
        console.log(`  - ${table.table_name} (${table.table_type})`)
      })
    }
  } catch (err) {
    console.error('🔥 Error fetching schema:', err)
  }
}

// Start the interactive session
console.log('🚀 Supabase-Ollama Query System Started!')
console.log('Ask questions about your database and get AI-powered answers.')
console.log('\n📋 Available commands:')
console.log('  - Type "schema" to see all available tables/views')
console.log('  - Type "exit" to quit')
console.log('  - Ask any question about your data')

// Enhanced askQuestion function
const askQuestionEnhanced = () => {
  rl.question('\n💬 Enter your question (or "schema"/"exit"): ', async (question) => {
    if (question.toLowerCase() === 'exit') {
      console.log('👋 Goodbye!')
      rl.close()
      return
    }

    if (question.toLowerCase() === 'schema') {
      await listDatabaseSchema()
      askQuestionEnhanced()
      return
    }

    if (question.toLowerCase().includes('quiz')) {
      // Try direct quiz queries
      console.log('\n🔍 Searching for quiz data...')

      try {
        // Try common quiz table names
        const quizTables = ['quiz', 'quizzes', 'questions', 'quiz_questions']

        for (const tableName of quizTables) {
          const { data, error } = await supabase
            .from(tableName)
            .select('*', { count: 'exact' })
            .limit(5)

          if (!error && data) {
            console.log(`✅ Found table "${tableName}" with ${data.length} records`)
            if (data.length > 0) {
              console.log('Sample data:', data[0])
            }
          }
        }
      } catch (err) {
        console.log('⚠️ Direct table access failed, using RPC...')
        await callOllama(question.trim())
      }

      askQuestionEnhanced()
      return
    }

    if (question.trim() === '') {
      console.log('⚠️ Please enter a valid question.')
      askQuestionEnhanced()
      return
    }

    await callOllama(question.trim())
    askQuestionEnhanced() // Ask for next question
  })
}

askQuestionEnhanced()
