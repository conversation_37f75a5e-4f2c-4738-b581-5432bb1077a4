CREATE OR REPLACE FUNCTION public.fn_get_related_table_data(question_text text)
RETURNS jsonb
LANGUAGE plpgsql
AS $function$
DECLARE
    words TEXT[];
    search_words TEXT[];
    table_record RECORD;
    view_record RECORD;
    function_record RECORD;
    row_result JSONB;
    result JSONB := '{}';
    tables_data JSONB := '{}';
    views_data JSONB := '{}';
    functions_data JSONB := '{}';
    all_tables TEXT[];
    all_views TEXT[];
    matched_tables TEXT[] := '{}';
    matched_views TEXT[] := '{}';
    word TEXT;
    table_name_clean TEXT;
    similarity_score INT;
    max_records INT := 100;
BEGIN
    -- Extract and clean words from the question
    words := regexp_split_to_array(lower(regexp_replace(question_text, '[^\w\s]', '', 'g')), '\s+');

    -- Remove common stopwords but keep domain-specific terms
    SELECT array_agg(w.word_value) INTO search_words
    FROM unnest(words) AS w(word_value)
    WHERE w.word_value NOT IN (
        'what', 'is', 'the', 'of', 'where', 'how', 'when', 'who', 'a', 'an', 'do', 'does', 'can',
        'will', 'would', 'should', 'could', 'me', 'my', 'i', 'you', 'your', 'his', 'her', 'their', 'our',
        'this', 'that', 'these', 'those', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by',
        'from', 'as', 'be', 'have', 'has', 'had', 'get', 'got', 'give', 'gave', 'show', 'find', 'tell',
        'there', 'here'
    ) AND length(w.word_value) > 1;

    -- Get all available tables and views from both public and smart_learn schemas
    SELECT array_agg(table_schema || '.' || table_name) INTO all_tables
    FROM information_schema.tables
    WHERE table_schema IN ('public', 'smart_learn') AND table_type = 'BASE TABLE';

    SELECT array_agg(table_schema || '.' || table_name) INTO all_views
    FROM information_schema.tables
    WHERE table_schema IN ('public', 'smart_learn') AND table_type = 'VIEW';

    -- If no specific search words, return data from key tables/views (fallback)
    IF search_words IS NULL OR array_length(search_words, 1) = 0 THEN
        -- Return sample data from all tables and views
        FOR table_record IN
            SELECT table_schema, table_name FROM information_schema.tables
            WHERE table_schema IN ('public', 'smart_learn') AND table_type = 'BASE TABLE'
            ORDER BY table_name LIMIT 10
        LOOP
            EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %I.%I LIMIT 20) t', table_record.table_schema, table_record.table_name)
            INTO row_result;

            tables_data := jsonb_set(
                tables_data,
                ARRAY[table_record.table_schema || '.' || table_record.table_name],
                COALESCE(row_result, '[]'::jsonb)
            );
        END LOOP;

        FOR view_record IN
            SELECT table_schema, table_name FROM information_schema.tables
            WHERE table_schema IN ('public', 'smart_learn') AND table_type = 'VIEW'
            ORDER BY table_name LIMIT 10
        LOOP
            EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %I.%I LIMIT 20) t', view_record.table_schema, view_record.table_name)
            INTO row_result;

            views_data := jsonb_set(
                views_data,
                ARRAY[view_record.table_schema || '.' || view_record.table_name],
                COALESCE(row_result, '[]'::jsonb)
            );
        END LOOP;
    ELSE
        -- Smart matching for tables
        FOREACH word IN ARRAY search_words
        LOOP
            -- Direct table name matches
            FOR table_record IN
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                  AND (
                      lower(table_name) LIKE '%' || word || '%'
                      OR lower(table_name) = word
                      OR word = ANY(string_to_array(lower(table_name), '_'))
                  )
            LOOP
                IF NOT (table_record.table_name = ANY(matched_tables)) THEN
                    matched_tables := array_append(matched_tables, table_record.table_name);
                END IF;
            END LOOP;

            -- Column name matches (find tables with matching column names)
            FOR table_record IN
                SELECT DISTINCT t.table_name
                FROM information_schema.tables t
                JOIN information_schema.columns c ON t.table_name = c.table_name
                WHERE t.table_schema = 'public' AND t.table_type = 'BASE TABLE'
                  AND c.table_schema = 'public'
                  AND lower(c.column_name) LIKE '%' || word || '%'
            LOOP
                IF NOT (table_record.table_name = ANY(matched_tables)) THEN
                    matched_tables := array_append(matched_tables, table_record.table_name);
                END IF;
            END LOOP;
        END LOOP;

        -- Smart matching for views
        FOREACH word IN ARRAY search_words
        LOOP
            FOR view_record IN
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public' AND table_type = 'VIEW'
                  AND (
                      lower(table_name) LIKE '%' || word || '%'
                      OR lower(table_name) = word
                      OR word = ANY(string_to_array(lower(table_name), '_'))
                  )
            LOOP
                IF NOT (view_record.table_name = ANY(matched_views)) THEN
                    matched_views := array_append(matched_views, view_record.table_name);
                END IF;
            END LOOP;

            -- Column name matches for views
            FOR view_record IN
                SELECT DISTINCT t.table_name
                FROM information_schema.tables t
                JOIN information_schema.columns c ON t.table_name = c.table_name
                WHERE t.table_schema = 'public' AND t.table_type = 'VIEW'
                  AND c.table_schema = 'public'
                  AND lower(c.column_name) LIKE '%' || word || '%'
            LOOP
                IF NOT (view_record.table_name = ANY(matched_views)) THEN
                    matched_views := array_append(matched_views, view_record.table_name);
                END IF;
            END LOOP;
        END LOOP;

        -- If no matches found, include some default tables
        IF array_length(matched_tables, 1) = 0 AND array_length(matched_views, 1) = 0 THEN
            -- Include tables that might be relevant based on common patterns
            FOR table_record IN
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                ORDER BY table_name LIMIT 5
            LOOP
                matched_tables := array_append(matched_tables, table_record.table_name);
            END LOOP;
        END IF;

        -- Get data from matched tables
        IF matched_tables IS NOT NULL THEN
            FOREACH table_name_clean IN ARRAY matched_tables
            LOOP
                BEGIN
                    EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %I LIMIT %s) t', table_name_clean, max_records)
                    INTO row_result;
                    
                    tables_data := jsonb_set(
                        tables_data,
                        ARRAY[table_name_clean],
                        COALESCE(row_result, '[]'::jsonb)
                    );
                EXCEPTION WHEN OTHERS THEN
                    -- Skip tables that can't be accessed
                    CONTINUE;
                END;
            END LOOP;
        END IF;

        -- Get data from matched views
        IF matched_views IS NOT NULL THEN
            FOREACH table_name_clean IN ARRAY matched_views
            LOOP
                BEGIN
                    EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %I LIMIT %s) t', table_name_clean, max_records)
                    INTO row_result;
                    
                    views_data := jsonb_set(
                        views_data,
                        ARRAY[table_name_clean],
                        COALESCE(row_result, '[]'::jsonb)
                    );
                EXCEPTION WHEN OTHERS THEN
                    -- Skip views that can't be accessed
                    CONTINUE;
                END;
            END LOOP;
        END IF;
    END IF;

    -- Get function information
    FOR function_record IN
        SELECT routine_name, routine_type
        FROM information_schema.routines
        WHERE routine_schema = 'public'
          AND routine_type = 'FUNCTION'
          AND (
              search_words IS NULL 
              OR EXISTS (
                  SELECT 1 FROM unnest(search_words) AS w
                  WHERE lower(routine_name) LIKE '%' || w || '%'
              )
          )
        LIMIT 20
    LOOP
        functions_data := jsonb_set(
            functions_data,
            ARRAY[function_record.routine_name],
            jsonb_build_object(
                'type', function_record.routine_type,
                'name', function_record.routine_name
            )
        );
    END LOOP;

    -- Build final result
    result := jsonb_build_object(
        'tables', tables_data,
        'views', views_data,
        'functions', functions_data,
        'search_terms', to_jsonb(search_words),
        'matched_tables', to_jsonb(matched_tables),
        'matched_views', to_jsonb(matched_views)
    );

    RETURN result;
END;
$function$;
