-- Updated function to access smart_learn schema data
CREATE OR REPLACE FUNCTION public.fn_get_related_table_data(question_text text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER  -- This is important for cross-schema access
AS $function$
DECLARE
    words TEXT[];
    search_words TEXT[];
    table_record RECORD;
    view_record RECORD;
    row_result JSONB;
    result JSONB := '{}';
    tables_data JSONB := '{}';
    views_data JSONB := '{}';
    functions_data JSONB := '{}';
    matched_tables TEXT[] := '{}';
    matched_views TEXT[] := '{}';
    word TEXT;
    table_name_clean TEXT;
    max_records INT := 50;
    schema_name TEXT;
BEGIN
    -- Extract and clean words from the question
    words := regexp_split_to_array(lower(regexp_replace(question_text, '[^\w\s]', '', 'g')), '\s+');

    -- Remove common stopwords but keep domain-specific terms
    SELECT array_agg(w.word_value) INTO search_words
    FROM unnest(words) AS w(word_value)
    WHERE w.word_value NOT IN (
        'what', 'is', 'the', 'of', 'where', 'how', 'when', 'who', 'a', 'an', 'do', 'does', 'can',
        'will', 'would', 'should', 'could', 'me', 'my', 'i', 'you', 'your', 'his', 'her', 'their', 'our',
        'this', 'that', 'these', 'those', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by',
        'from', 'as', 'be', 'have', 'has', 'had', 'get', 'got', 'give', 'gave', 'show', 'find', 'tell',
        'there', 'here'
    ) AND length(w.word_value) > 1;

    -- If no specific search words, return data from key tables
    IF search_words IS NULL OR array_length(search_words, 1) = 0 THEN
        search_words := ARRAY['category', 'quiz', 'user', 'course', 'activity'];
    END IF;

    -- Search in both public and smart_learn schemas
    FOR schema_name IN SELECT unnest(ARRAY['public', 'smart_learn'])
    LOOP
        -- Smart matching for tables in current schema
        FOREACH word IN ARRAY search_words
        LOOP
            -- Direct table name matches
            FOR table_record IN
                SELECT t.table_name, t.table_schema
                FROM information_schema.tables t
                WHERE t.table_schema = schema_name 
                  AND t.table_type = 'BASE TABLE'
                  AND (
                      lower(t.table_name) LIKE '%' || word || '%'
                      OR lower(t.table_name) = word
                      OR word = ANY(string_to_array(lower(t.table_name), '_'))
                  )
            LOOP
                IF NOT (table_record.table_name = ANY(matched_tables)) THEN
                    matched_tables := array_append(matched_tables, table_record.table_schema || '.' || table_record.table_name);
                END IF;
            END LOOP;

            -- Column name matches (find tables with matching column names)
            FOR table_record IN
                SELECT DISTINCT t.table_name, t.table_schema
                FROM information_schema.tables t
                JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
                WHERE t.table_schema = schema_name 
                  AND t.table_type = 'BASE TABLE'
                  AND lower(c.column_name) LIKE '%' || word || '%'
            LOOP
                IF NOT ((table_record.table_schema || '.' || table_record.table_name) = ANY(matched_tables)) THEN
                    matched_tables := array_append(matched_tables, table_record.table_schema || '.' || table_record.table_name);
                END IF;
            END LOOP;
        END LOOP;

        -- Smart matching for views in current schema
        FOREACH word IN ARRAY search_words
        LOOP
            FOR view_record IN
                SELECT t.table_name, t.table_schema
                FROM information_schema.tables t
                WHERE t.table_schema = schema_name 
                  AND t.table_type = 'VIEW'
                  AND (
                      lower(t.table_name) LIKE '%' || word || '%'
                      OR lower(t.table_name) = word
                      OR word = ANY(string_to_array(lower(t.table_name), '_'))
                  )
            LOOP
                IF NOT ((view_record.table_schema || '.' || view_record.table_name) = ANY(matched_views)) THEN
                    matched_views := array_append(matched_views, view_record.table_schema || '.' || view_record.table_name);
                END IF;
            END LOOP;
        END LOOP;
    END LOOP;

    -- Get data from matched tables
    IF matched_tables IS NOT NULL THEN
        FOREACH table_name_clean IN ARRAY matched_tables
        LOOP
            BEGIN
                EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %s LIMIT %s) t', table_name_clean, max_records)
                INTO row_result;
                
                tables_data := jsonb_set(
                    tables_data,
                    ARRAY[table_name_clean],
                    COALESCE(row_result, '[]'::jsonb)
                );
            EXCEPTION WHEN OTHERS THEN
                -- Skip tables that can't be accessed
                CONTINUE;
            END;
        END LOOP;
    END IF;

    -- Get data from matched views
    IF matched_views IS NOT NULL THEN
        FOREACH table_name_clean IN ARRAY matched_views
        LOOP
            BEGIN
                EXECUTE format('SELECT jsonb_agg(t) FROM (SELECT * FROM %s LIMIT %s) t', table_name_clean, max_records)
                INTO row_result;
                
                views_data := jsonb_set(
                    views_data,
                    ARRAY[table_name_clean],
                    COALESCE(row_result, '[]'::jsonb)
                );
            EXCEPTION WHEN OTHERS THEN
                -- Skip views that can't be accessed
                CONTINUE;
            END;
        END LOOP;
    END IF;

    -- Build final result
    result := jsonb_build_object(
        'tables', tables_data,
        'views', views_data,
        'functions', functions_data,
        'search_terms', to_jsonb(search_words),
        'matched_tables', to_jsonb(matched_tables),
        'matched_views', to_jsonb(matched_views)
    );

    RETURN result;
END;
$function$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.fn_get_related_table_data(text) TO anon, authenticated;

-- If you need to grant access to smart_learn schema (run this if you have permission)
-- GRANT USAGE ON SCHEMA smart_learn TO anon, authenticated;
-- GRANT SELECT ON ALL TABLES IN SCHEMA smart_learn TO anon, authenticated;
-- GRANT SELECT ON ALL SEQUENCES IN SCHEMA smart_learn TO anon, authenticated;
