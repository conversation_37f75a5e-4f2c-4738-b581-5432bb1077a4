"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports._convertToOpenAITool = void 0;
const function_calling_1 = require("@langchain/core/utils/function_calling");
const openai_js_1 = require("./openai.cjs");
/**
 * Formats a tool in either OpenAI format, or LangChain structured tool format
 * into an OpenAI tool format. If the tool is already in OpenAI format, return without
 * any changes. If it is in LangChain structured tool format, convert it to OpenAI tool format
 * using OpenAI's `zodFunction` util, falling back to `convertToOpenAIFunction` if the parameters
 * returned from the `zodFunction` util are not defined.
 *
 * @param {BindToolsInput} tool The tool to convert to an OpenAI tool.
 * @param {Object} [fields] Additional fields to add to the OpenAI tool.
 * @returns {ToolDefinition} The inputted tool in OpenAI tool format.
 */
function _convertToOpenAITool(
// eslint-disable-next-line @typescript-eslint/no-explicit-any
tool, fields) {
    let toolDef;
    if ((0, function_calling_1.isLangChainTool)(tool)) {
        toolDef = (0, openai_js_1.formatToOpenAITool)(tool);
    }
    else {
        toolDef = tool;
    }
    if (fields?.strict !== undefined) {
        toolDef.function.strict = fields.strict;
    }
    return toolDef;
}
exports._convertToOpenAITool = _convertToOpenAITool;
